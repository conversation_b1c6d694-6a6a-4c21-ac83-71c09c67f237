#!/usr/bin/env node

import { ForaChatApp } from '../ForaChatApp';
import { logger } from '../utils/Logger';
import * as fs from 'fs';
import * as path from 'path';
import * as http from 'http';
import { v4 as uuidv4 } from 'uuid';

interface TestPrompt {
  id: string;
  prompt: string;
  expectedTheme?: string;
  expectedSkills?: string[];
  timeout?: number; // in milliseconds
}

interface TestResult {
  id: string;
  prompt: string;
  success: boolean;
  response?: any;
  error?: string;
  duration: number;
  timestamp: string;
  conversationId?: number;
  messageCount?: number;
  delayedMessages?: any[];
}

interface TestConfig {
  serverUrl?: string;
  serverPort?: number;
  maxDelayedWaitTime?: number; // Maximum time to wait for delayed thoughts
  logLevel?: 'verbose' | 'normal' | 'minimal';
  outputFile?: string;
}

export class IntegrationTestRunner {
  private app: ForaChatApp | null = null;
  private config: TestConfig;
  private results: TestResult[] = [];
  private sessionId: string;

  constructor(config: TestConfig = {}) {
    this.config = {
      serverUrl: 'localhost',
      serverPort: 3000,
      maxDelayedWaitTime: 30000, // 30 seconds
      logLevel: 'normal',
      ...config
    };
    this.sessionId = uuidv4();
  }

  /**
   * Load test prompts from a file
   */
  loadPromptsFromFile(filePath: string): TestPrompt[] {
    const fullPath = path.resolve(filePath);
    
    if (!fs.existsSync(fullPath)) {
      throw new Error(`Prompts file not found: ${fullPath}`);
    }

    const content = fs.readFileSync(fullPath, 'utf-8');
    const lines = content.split('\n').filter(line => line.trim() && !line.startsWith('#'));
    
    return lines.map((line, index) => ({
      id: `prompt_${index + 1}`,
      prompt: line.trim(),
      timeout: 60000 // 1 minute default timeout per prompt
    }));
  }

  /**
   * Load test prompts from JSON file with more detailed configuration
   */
  loadPromptsFromJSON(filePath: string): TestPrompt[] {
    const fullPath = path.resolve(filePath);
    
    if (!fs.existsSync(fullPath)) {
      throw new Error(`Prompts JSON file not found: ${fullPath}`);
    }

    const content = fs.readFileSync(fullPath, 'utf-8');
    const data = JSON.parse(content);
    
    if (!Array.isArray(data.prompts)) {
      throw new Error('JSON file must contain a "prompts" array');
    }

    return data.prompts.map((prompt: any, index: number) => ({
      id: prompt.id || `prompt_${index + 1}`,
      prompt: prompt.text || prompt.prompt,
      expectedTheme: prompt.expectedTheme,
      expectedSkills: prompt.expectedSkills,
      timeout: prompt.timeout || 60000
    }));
  }

  /**
   * Start the ForaChat server
   */
  async startServer(): Promise<void> {
    // First check if server is already running
    try {
      await this.waitForServerReady(3); // Quick check with fewer attempts
      this.log('✅ ForaChat server is already running', 'normal');
      return;
    } catch (error) {
      this.log('🚀 Starting ForaChat server for integration testing...', 'verbose');
    }

    this.app = new ForaChatApp({ quietMode: true });
    await this.app.start();

    this.log('✅ ForaChat server started successfully', 'normal');

    // Wait for server to be fully ready
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Verify server is responding
    await this.waitForServerReady();
  }

  /**
   * Wait for server to be ready by checking health endpoint
   */
  private async waitForServerReady(maxAttempts: number = 10): Promise<void> {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const response = await this.makeHttpRequest('/health', 'GET');
        if (response.status === 'healthy' || response.status === 'ok') {
          this.log('✅ Server health check passed', 'verbose');
          return;
        } else {
          throw new Error(`Unexpected health status: ${response.status}`);
        }
      } catch (error) {
        this.log(`⏳ Server health check attempt ${attempt}/${maxAttempts} failed: ${(error as Error).message}`, 'verbose');
        if (attempt === maxAttempts) {
          throw new Error(`Server failed to become ready after ${maxAttempts} attempts: ${(error as Error).message}`);
        }
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  }

  /**
   * Send a message to the chat endpoint
   */
  private async sendMessage(prompt: string): Promise<any> {
    const postData = JSON.stringify({ text: prompt });
    return this.makeHttpRequest('/chat', 'POST', postData);
  }

  /**
   * Poll for delayed messages
   */
  private async pollForDelayedMessages(conversationId: number, lastMessageId: number = 0): Promise<any[]> {
    const delayedMessages: any[] = [];
    const startTime = Date.now();
    let currentLastMessageId = lastMessageId;

    while (Date.now() - startTime < this.config.maxDelayedWaitTime!) {
      try {
        const response = await this.makeHttpRequest(
          `/conversation/${conversationId}`,
          'GET'
        );

        if (response.messages && response.messages.length > 0) {
          // Filter for new messages that came after our last known message ID
          const newMessages = response.messages.filter((msg: any) =>
            msg.id > currentLastMessageId && msg.character !== 'user'
          );

          if (newMessages.length > 0) {
            delayedMessages.push(...newMessages);
            currentLastMessageId = Math.max(...newMessages.map((msg: any) => msg.id));
            this.log(`📨 Received ${newMessages.length} delayed messages`, 'verbose');
          }
        }

        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        this.log(`⚠️ Error polling for delayed messages: ${(error as Error).message}`, 'verbose');
        break;
      }
    }

    return delayedMessages;
  }

  /**
   * Make HTTP request to the server
   */
  private async makeHttpRequest(path: string, method: string, data?: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const options = {
        hostname: this.config.serverUrl,
        port: this.config.serverPort,
        path,
        method,
        headers: {
          'Content-Type': 'application/json',
          ...(data && { 'Content-Length': Buffer.byteLength(data) })
        }
      };

      this.log(`🌐 Making ${method} request to http://${options.hostname}:${options.port}${path}`, 'verbose');

      const req = http.request(options, (res) => {
        let responseData = '';

        res.on('data', (chunk) => {
          responseData += chunk;
        });

        res.on('end', () => {
          try {
            if (responseData.trim()) {
              const parsed = JSON.parse(responseData);
              resolve(parsed);
            } else {
              resolve({});
            }
          } catch (error) {
            reject(new Error(`Failed to parse response: ${responseData}`));
          }
        });
      });

      req.on('error', (error) => {
        this.log(`❌ HTTP request error: ${error.message}`, 'verbose');
        reject(error);
      });

      if (data) {
        req.write(data);
      }
      req.end();
    });
  }

  /**
   * Run a single test prompt
   */
  async runSingleTest(testPrompt: TestPrompt): Promise<TestResult> {
    const startTime = Date.now();
    const timestamp = new Date().toISOString();
    
    this.log(`\n🧪 Running test: ${testPrompt.id}`, 'normal');
    this.log(`📝 Prompt: "${testPrompt.prompt}"`, 'normal');

    try {
      // Send the message
      const response = await Promise.race([
        this.sendMessage(testPrompt.prompt),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), testPrompt.timeout!)
        )
      ]);

      this.log(`✅ Initial response received`, 'verbose');
      this.log(`📊 Response: ${JSON.stringify(response, null, 2)}`, 'verbose');

      // Poll for delayed messages if we have a conversation ID
      let delayedMessages: any[] = [];
      if (response.conversationId) {
        this.log(`⏳ Polling for delayed messages...`, 'verbose');
        const lastMessageId = response.reply ? Math.max(...response.reply.map((msg: any) => msg.id || 0)) : 0;
        delayedMessages = await this.pollForDelayedMessages(response.conversationId, lastMessageId);
      }

      const duration = Date.now() - startTime;
      const totalMessages = (response.reply?.length || 0) + delayedMessages.length;

      const result: TestResult = {
        id: testPrompt.id,
        prompt: testPrompt.prompt,
        success: true,
        response,
        duration,
        timestamp,
        conversationId: response.conversationId,
        messageCount: totalMessages,
        delayedMessages
      };

      this.log(`✅ Test completed successfully in ${duration}ms`, 'normal');
      this.log(`📈 Total messages: ${totalMessages} (${response.reply?.length || 0} immediate + ${delayedMessages.length} delayed)`, 'normal');

      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      const result: TestResult = {
        id: testPrompt.id,
        prompt: testPrompt.prompt,
        success: false,
        error: (error as Error).message,
        duration,
        timestamp
      };

      this.log(`❌ Test failed: ${(error as Error).message}`, 'normal');
      return result;
    }
  }

  /**
   * Run all test prompts
   */
  async runTests(prompts: TestPrompt[]): Promise<TestResult[]> {
    this.log(`\n🎯 Starting integration test run with ${prompts.length} prompts`, 'normal');
    this.log(`🔧 Session ID: ${this.sessionId}`, 'verbose');
    
    const results: TestResult[] = [];

    for (let i = 0; i < prompts.length; i++) {
      const prompt = prompts[i];
      this.log(`\n📋 Progress: ${i + 1}/${prompts.length}`, 'normal');
      
      const result = await this.runSingleTest(prompt);
      results.push(result);
      this.results.push(result);

      // Brief pause between tests
      if (i < prompts.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    return results;
  }

  /**
   * Generate test report
   */
  generateReport(results: TestResult[]): string {
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);
    const avgDuration = totalDuration / results.length;

    let report = `
# Integration Test Report
Generated: ${new Date().toISOString()}
Session ID: ${this.sessionId}

## Summary
- Total Tests: ${results.length}
- Successful: ${successful}
- Failed: ${failed}
- Success Rate: ${((successful / results.length) * 100).toFixed(1)}%
- Total Duration: ${(totalDuration / 1000).toFixed(2)}s
- Average Duration: ${(avgDuration / 1000).toFixed(2)}s

## Test Results
`;

    results.forEach(result => {
      report += `
### ${result.id} ${result.success ? '✅' : '❌'}
- **Prompt**: "${result.prompt}"
- **Duration**: ${(result.duration / 1000).toFixed(2)}s
- **Timestamp**: ${result.timestamp}
`;

      if (result.success) {
        report += `- **Conversation ID**: ${result.conversationId}
- **Message Count**: ${result.messageCount}
- **Theme**: ${result.response?.theme || 'N/A'}
- **Skills**: ${result.response?.skills?.join(', ') || 'N/A'}
`;
      } else {
        report += `- **Error**: ${result.error}
`;
      }
    });

    return report;
  }

  /**
   * Save results to file
   */
  async saveResults(results: TestResult[], outputPath?: string): Promise<void> {
    const filePath = outputPath || this.config.outputFile || `integration-test-results-${Date.now()}.json`;
    
    const output = {
      sessionId: this.sessionId,
      timestamp: new Date().toISOString(),
      config: this.config,
      summary: {
        total: results.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
        totalDuration: results.reduce((sum, r) => sum + r.duration, 0)
      },
      results
    };

    fs.writeFileSync(filePath, JSON.stringify(output, null, 2));
    this.log(`💾 Results saved to: ${filePath}`, 'normal');

    // Also save markdown report
    const reportPath = filePath.replace('.json', '.md');
    const report = this.generateReport(results);
    fs.writeFileSync(reportPath, report);
    this.log(`📄 Report saved to: ${reportPath}`, 'normal');
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    this.log('\n🧹 Cleaning up...', 'verbose');
    
    if (this.app) {
      // Note: ForaChatApp doesn't have a stop method, so we'll just set it to null
      // In a real implementation, you might want to add a proper shutdown method
      this.app = null;
    }
    
    this.log('✅ Cleanup completed', 'verbose');
  }

  /**
   * Log message based on configured log level
   */
  private log(message: string, level: 'verbose' | 'normal' | 'minimal' = 'normal'): void {
    const levels = { minimal: 0, normal: 1, verbose: 2 };
    const currentLevel = levels[this.config.logLevel!];
    const messageLevel = levels[level];

    if (messageLevel <= currentLevel) {
      const timestamp = new Date().toLocaleTimeString();
      console.log(`[${timestamp}] ${message}`);
    }
  }
}
